FROM docker.zysproxy.online/ubuntu:{{VERSION}}

# 接收构建参数
ARG CRYPTO_VERSION
ARG ARCH
ARG INSTALL_CARD_MNGR=false
ARG INSTALL_SC62=false
ARG INSTALL_SC34=false
ARG OS_TYPE=ubuntu
ARG USE_KYLIN_CRYPTO=false

# 设置环境变量（架构无关）
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    TERM=xterm-256color \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai

# 添加元数据
LABEL maintainer="维护团队 <<EMAIL>>" \
      description="基础Ubuntu {{VERSION}} 镜像，含NETCA_CRYPTO支持" \
      version="{{VERSION}}" \
      codename="{{CODENAME}}" \
      crypto_version="${CRYPTO_VERSION}" \
      architecture="${ARCH}" \
      card_manager="${INSTALL_CARD_MNGR}" \
      sansec_sc62="${INSTALL_SC62}" \
      sansec_sc34="${INSTALL_SC34}"

# 配置apt源并安装必要软件包
RUN set -ex \
    # 1. 配置基础的apt源, 并标记为[trusted=yes]以忽略GPG验证
    && ARCH=$(dpkg --print-architecture) \
    && if [ "$ARCH" = "arm64" ] || [ "$ARCH" = "aarch64" ]; then \
        echo "deb [trusted=yes] http://ports.ubuntu.com/ubuntu-ports {{CODENAME}} main restricted universe multiverse" > /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://ports.ubuntu.com/ubuntu-ports {{CODENAME}}-updates main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://ports.ubuntu.com/ubuntu-ports {{CODENAME}}-backports main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://ports.ubuntu.com/ubuntu-ports {{CODENAME}}-security main restricted universe multiverse" >> /etc/apt/sources.list; \
    else \
        echo "deb [trusted=yes] http://mirrors.aliyun.com/ubuntu/ {{CODENAME}} main restricted universe multiverse" > /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://mirrors.aliyun.com/ubuntu/ {{CODENAME}}-updates main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://mirrors.aliyun.com/ubuntu/ {{CODENAME}}-backports main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://mirrors.aliyun.com/ubuntu/ {{CODENAME}}-security main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://archive.ubuntu.com/ubuntu/ {{CODENAME}} main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://archive.ubuntu.com/ubuntu/ {{CODENAME}}-updates main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://archive.ubuntu.com/ubuntu/ {{CODENAME}}-backports main restricted universe multiverse" >> /etc/apt/sources.list \
        && echo "deb [trusted=yes] http://security.ubuntu.com/ubuntu/ {{CODENAME}}-security main restricted universe multiverse" >> /etc/apt/sources.list; \
    fi \
    # 2. 更新包列表并安装所有需要的软件包
    && apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
        apt-utils \
        {{PACKAGES}} \
        dialog \
        libterm-readline-perl-perl \
        sqlite3 \
        libsqlite3-dev \
        libgmp10 \
        libgmp3-dev \
        libldap-2.5-0 \
        libssl3 \
        libssl-dev \
        openssl \
        libzip4 \
        libxml2 \
        unzip \
        build-essential \
        pciutils \
        dnsutils \
        kmod \
        tzdata \
    # 3. 配置时区和其他
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && dpkg-reconfigure -f noninteractive tzdata \
    # 配置库文件环境变量
    && echo "export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib64" >> /etc/profile \
    && echo "export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib64" >> /etc/bash.bashrc \
    # 创建必要的符号链接
    && find /usr/lib -name "libgmp.so.10" -exec ln -sf {} /usr/lib64/libgmp.so.3 \; 2>/dev/null || true \
    && find /usr/lib -name "libzip.so.4" -exec sh -c 'ln -sf "$1" "${1%/*}/libzip.so.1"' _ {} \; 2>/dev/null || true \
    && KERNEL_SRC="/lib/modules/${KERNEL_VERSION}/build" \
    && if [ -d "${KERNEL_SRC}/arch/x86" ] && [ ! -d "${KERNEL_SRC}/arch/x64" ]; then \
        echo "创建arch/x64到arch/x86的符号链接" \
        && ln -sf "${KERNEL_SRC}/arch/x86" "${KERNEL_SRC}/arch/x64"; \
    fi \
    # ==================== 修改点开始 ====================
    # 4. 仅针对 arm64 架构，创建一个 "shim" 脚本来全局绕过有问题的 bash 内置 test 命令
    && if [ "$ARCH" = "arm64" ] || [ "$ARCH" = "aarch64" ]; then \
        echo "Applying test command shim for arm64 architecture..."; \
        echo '#!/bin/sh' > /usr/local/bin/test; \
        echo '/usr/bin/test "$@"' >> /usr/local/bin/test; \
        chmod +x /usr/local/bin/test; \
    fi \
    # ==================== 修改点结束 ====================
    # 5. 清理
    && apt-get clean \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# Copy NETCA_CRYPTO files (构建脚本会将文件复制到构建上下文)
COPY shared/crypto-files/${CRYPTO_VERSION}/${ARCH}/ /tmp/NETCA_CRYPTO_linux32_64/

# Copy Kylin crypto files if using Kylin crypto method (按架构复制)
COPY shared/crypto-files/kylin/${ARCH}/ /tmp/kylin-crypto-files/

# Install NETCA_CRYPTO and related components
RUN set -ex \
    && mkdir -p /usr/lib64 /var/log \
    # 安装NETCA CRYPTO
    && if [ "$USE_KYLIN_CRYPTO" = "true" ]; then \
        echo "使用Kylin方式安装NETCA_CRYPTO，使用预编译的加密库文件..." \
        && echo "正在复制Kylin加密库文件到/usr/lib64/..." \
        && find /tmp/kylin-crypto-files -name "*.so*" -exec cp {} /usr/lib64/ \; \
        && chmod 755 /usr/lib64/*.so* \
        && echo "Kylin加密库文件安装完成"; \
    else \
        echo "使用标准NETCA CRYPTO安装流程..." \
        && cd /tmp/NETCA_CRYPTO_linux32_64 \
        && chmod +x setup.sh \
        && echo "开始安装NETCA CRYPTO组件..." \
        && bash -x ./setup.sh /usr/lib64 CRYPTO move; \
    fi \
    # 根据条件安装密码卡管理JNI库（仅在标准模式下）
    && if [ "$INSTALL_CARD_MNGR" = "true" ] && [ "$USE_KYLIN_CRYPTO" != "true" ]; then \
        echo "开始安装NetcaCardMngr组件..." \
        && cd /tmp/NETCA_CRYPTO_linux32_64 \
        && bash -x ./setup.sh /usr/lib64 NetcaCardMngr move; \
    fi \
    # 根据条件安装SansecCard_SC62组件（仅在标准模式下）
    && if [ "$INSTALL_SC62" = "true" ] && [ "$USE_KYLIN_CRYPTO" != "true" ]; then \
        echo "开始安装SansecCard_SC62组件..." \
        && cd /tmp/NETCA_CRYPTO_linux32_64 \
        && bash -x ./setup.sh /usr/lib64 SansecCard_SC62 Library ; \
    fi \
    # 根据条件安装SansecCard_SC34组件（仅在标准模式下）
    && if [ "$INSTALL_SC34" = "true" ] && [ "$USE_KYLIN_CRYPTO" != "true" ]; then \
        echo "开始安装SansecCard_SC34组件..." \
        && cd /tmp/NETCA_CRYPTO_linux32_64 \
        && bash -x ./setup.sh /usr/lib64 SansecCard Library ; \
    fi \
    # 创建必要的库文件链接（自动查找系统库）
    && for lib in libsqlite3.so.0 libssl.so.3 libcrypto.so.3; do \
        if [ ! -e "/usr/lib64/$lib" ]; then \
          lib_path=$(find /usr/lib -name "$lib" 2>/dev/null | head -1); \
          [ -n "$lib_path" ] && ln -sf "$lib_path" "/usr/lib64/$lib"; \
        fi \
    done \
    # 清理
    && rm -rf /tmp/NETCA_CRYPTO_linux32_64 \
    && rm -rf /tmp/kylin-crypto-files

WORKDIR /app

CMD ["/bin/bash"]
